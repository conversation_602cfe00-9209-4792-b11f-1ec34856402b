import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';

// Create an MCP server
const server = new McpServer({
  name: 'home-mcp',
  version: '0.1.0',
});

// Add a simple ping tool that responds with "pong"
server.tool(
  'ping',
  {}, // No parameters needed for ping
  () => ({
    content: [{ type: 'text', text: 'pong' }],
  })
);

// Start the server with stdio transport
const transport = new StdioServerTransport();
await server.connect(transport);
